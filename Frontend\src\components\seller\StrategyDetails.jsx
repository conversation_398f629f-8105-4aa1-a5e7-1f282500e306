import React from "react";
import { useParams } from "react-router-dom";
import SellerLayout from "./SellerLayout";
import "../../styles/StrategyDetails.css";
import ourmissionimage from "../../assets/images/ourmissionimage.svg";
// Icons
import { MdPlayArrow, MdEdit, MdDelete } from "react-icons/md";

// Mock data for different strategies
const strategiesData = {
  1: {
    title:
      "<PERSON> - <PERSON>ills And Coaching Philosophies To Developing Toughness In Your Players",
    subtitle: "Basketball • Player Development",
    coach: "<PERSON>",
    coachTitle: "Basketball Coaching Clinic",
    description:
      'In This Course, Coach <PERSON> Presents "Drills And Coaching Philosophies To Developing Toughness In Your Players To Win On The Court And In Life." Coach <PERSON>gins With How Coaching Has Changed Over The Years And Some Advice For High School Coaches. Coach Then Speaks On What Goes Into Building The Culture From The Ground Up. Mindset That He Terms Are Famous For Coach Martin Also Gives Advice For College Coaches, Before Finishing With His Favorite Drills Which Help Teach A Certain Level Of Toughness.',
    coach<PERSON><PERSON>:
      "Coach <PERSON> is the Head Coach of The South Carolina Men's Basketball Team. A Position He Has Held Since 2012. He Is Best Known For Leading The Gamecocks To The 2017 Final Four, A Historic Achievement For The Program. Before His Time At South Carolina, Coach <PERSON> Served As The Head Coach At Kansas State University From 2007 To 2012, Where He Made Multiple Tournament Appearances, Including A Trip To The Elite 8 In 2010. That Same Year, He Was Named Big 12 Coach Of The Year, Recognizing His Exceptional Leadership And Success With The Wildcats.",
    category: "Basketball/Player Development",
    bookings: "347",
    duration: "1:17:28",
    views: "17",
    price: "49.99",
    image: "/api/placeholder/800/450",
  },
  2: {
    title: "John Calipari - Early Transition Offensive Concepts",
    subtitle: "Basketball • Offensive Strategy",
    coach: "John Calipari",
    coachTitle: "Basketball Coaching Expert",
    description:
      "Master the art of early transition offense with Coach John Calipari. This comprehensive course covers the fundamental concepts and advanced strategies for implementing effective transition offense in basketball. Learn how to create scoring opportunities through quick ball movement and player positioning.",
    coachBio:
      "Coach John Calipari is one of the most successful basketball coaches in college basketball history. Known for his innovative offensive strategies and player development, he has led multiple teams to championship success and has coached numerous NBA players.",
    category: "Basketball/Offensive Strategy",
    bookings: "289",
    duration: "1:32:15",
    views: "23",
    price: "39.99",
    image: "/api/placeholder/800/450",
  },
  3: {
    title: "WR Fundamentals PoA - Herman Wiggins",
    subtitle: "Football • Wide Receiver Training",
    coach: "Herman Wiggins",
    coachTitle: "Football Coaching Specialist",
    description:
      "Comprehensive wide receiver fundamentals training focusing on Point of Attack (PoA) techniques. This course covers route running, catching techniques, and positioning strategies essential for wide receiver success at all levels of football.",
    coachBio:
      "Coach Herman Wiggins brings years of experience in football coaching and player development. Specializing in wide receiver training, he has helped develop numerous successful players through his innovative training methods and attention to detail.",
    category: "Football/Wide Receiver",
    bookings: "156",
    duration: "58:42",
    views: "12",
    price: "29.99",
    image: "/api/placeholder/800/450",
  },
};

const StrategyDetails = () => {
  const { id } = useParams();
  const strategy = strategiesData[id] || strategiesData[1]; // Default to strategy 1 if not found

  return (
    <SellerLayout>
      <div className="StrategyDetails">
        {/* Video/Document Info Section */}
        <div className="StrategyDetails__info-header">
          <div className="StrategyDetails__info-content">
            <h2 className="StrategyDetails__info-title">Video/Document Info</h2>
          </div>
          <div className="StrategyDetails__info-actions">
            <button className="StrategyDetails__edit-btn">
              <MdEdit className="StrategyDetails__action-icon" />
              Edit
            </button>
            <button className="StrategyDetails__delete-btn">
              <MdDelete className="StrategyDetails__action-icon" />
              Delete
            </button>
          </div>
        </div>

        {/* Main Content */}
        <div className="StrategyDetails__content">
          <h3 className="StrategyDetails__strategy-title">{strategy.title}</h3>
          {/* Strategy Image */}
          <div className="StrategyDetails__image-container">
            <img
              src={ourmissionimage}
              // src={strategy.image}
              alt={strategy.title}
              className="StrategyDetails__image"
            />
            <div className="StrategyDetails__play-overlay">
              <MdPlayArrow className="StrategyDetails__play-icon" />
            </div>
          </div>
          {/* Description Section */}
          <div className="StrategyDetails__description-section">
            <h3 className="StrategyDetails__section-title">Description</h3>
            <p className="StrategyDetails__description">
              {strategy.description}
            </p>
          </div>
          {/* Coach Info */}
          <div className="StrategyDetails__coach-section">
            <h3 className="StrategyDetails__section-title">The Coach</h3>
            <div className="StrategyDetails__coach-info">
              <div className="StrategyDetails__coach-details">
                <h4 className="StrategyDetails__coach-name">
                  {strategy.coach}
                </h4>
                <p className="StrategyDetails__coach-title">
                  {strategy.coachTitle}
                </p>
                <p className="StrategyDetails__coach-description">
                  {strategy.coachBio}
                </p>
              </div>
            </div>
          </div>

          <div className="StrategyDetails__stats-and-steps">
            {/* Key Stats Section */}
            <div className="StrategyDetails__stats-section">
              <h3 className="StrategyDetails__section-title">
                Strategic Content Info
              </h3>
              <div className="StrategyDetails__stats-grid">
                <div className="StrategyDetails__stat-card">
                  <div className="StrategyDetails__stat-content">
                    <span className="StrategyDetails__stat-label">
                      Category
                    </span>
                    <span className="StrategyDetails__stat-value">
                      {strategy.category}
                    </span>
                  </div>
                </div>

                <div className="StrategyDetails__stat-card">
                  <div className="StrategyDetails__stat-content">
                    <span className="StrategyDetails__stat-label">
                      Bookings
                    </span>
                    <span className="StrategyDetails__stat-value">
                      {strategy.bookings}
                    </span>
                  </div>
                </div>

                <div className="StrategyDetails__stat-card">
                  <div className="StrategyDetails__stat-content">
                    <span className="StrategyDetails__stat-label">
                      Duration
                    </span>
                    <span className="StrategyDetails__stat-value">
                      {strategy.duration}
                    </span>
                  </div>
                </div>

                <div className="StrategyDetails__stat-card">
                  <div className="StrategyDetails__stat-content">
                    <span className="StrategyDetails__stat-label">Views</span>
                    <span className="StrategyDetails__stat-value">
                      {strategy.views}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div className="vertical-line"></div>
            {/* Strategy Steps */}
            <div className="StrategyDetails__steps-section">
              <h3 className="StrategyDetails__section-title">
                This Strategic Content Includes
              </h3>
              <div className="StrategyDetails__steps-list">
                <div className="StrategyDetails__step">
                  <span className="StrategyDetails__step-text">
                    24 Hours On-demand Video
                  </span>
                </div>
                <div className="StrategyDetails__step">
                  <span className="StrategyDetails__step-text">
                    Streaming From Mobile And TV
                  </span>
                </div>
                <div className="StrategyDetails__step">
                  <span className="StrategyDetails__step-text">
                    Lifetime Access
                  </span>
                </div>
                <div className="StrategyDetails__step">
                  <span className="StrategyDetails__step-text">
                    Certificate of Completion
                  </span>
                </div>
                <div className="StrategyDetails__step">
                  <span className="StrategyDetails__step-text">
                    100% Money Back Guarantee
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </SellerLayout>
  );
};

export default StrategyDetails;
