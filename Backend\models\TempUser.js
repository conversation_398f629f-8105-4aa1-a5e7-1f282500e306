const mongoose = require('mongoose');

const TempUserSchema = new mongoose.Schema(
  {
    firstName: {
      type: String,
      required: [true, 'Please add a first name'],
      trim: true,
      maxlength: [50, 'First name cannot be more than 50 characters']
    },
    lastName: {
      type: String,
      required: [true, 'Please add a last name'],
      trim: true,
      maxlength: [50, 'Last name cannot be more than 50 characters']
    },
    email: {
      type: String,
      required: [true, 'Please add an email'],
      unique: true,
      match: [
        /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
        'Please add a valid email'
      ]
    },
    mobile: {
      type: String,
      required: [true, 'Please add a mobile number'],
      maxlength: [20, 'Mobile number cannot be longer than 20 characters']
    },
    role: {
      type: String,
      enum: ['buyer', 'seller', 'admin'],
      default: 'buyer'
    },
    otpCode: String,
    otpExpire: Date,
    otpAttempts: {
      type: Number,
      default: 0
    },
    otpLastSent: Date,
    otpCooldownUntil: Date,
    createdAt: {
      type: Date,
      default: Date.now,
      expires: 3600 // Document expires after 1 hour
    }
  },
  {
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Generate OTP
TempUserSchema.methods.generateOTP = function() {
  // Generate a 6-digit OTP
  const otp = Math.floor(100000 + Math.random() * 900000).toString();

  // Set OTP and expiration (10 minutes)
  this.otpCode = otp;
  this.otpExpire = Date.now() + 10 * 60 * 1000;
  this.otpLastSent = Date.now();

  return otp;
};

// Check if user can request new OTP (cooldown check)
TempUserSchema.methods.canRequestOTP = function() {
  const cooldownSeconds = parseInt(process.env.OTP_RESEND_COOLDOWN) || 60;
  const maxAttempts = parseInt(process.env.OTP_MAX_ATTEMPTS) || 3;

  // Check if user has exceeded max attempts
  if (this.otpAttempts >= maxAttempts) {
    // Reset attempts after 1 hour
    if (this.otpCooldownUntil && this.otpCooldownUntil > Date.now()) {
      return {
        canRequest: false,
        reason: 'max_attempts_exceeded',
        cooldownUntil: this.otpCooldownUntil
      };
    } else {
      // Reset attempts after cooldown period
      this.otpAttempts = 0;
      this.otpCooldownUntil = undefined;
    }
  }

  // Check cooldown period
  if (this.otpLastSent) {
    const timeSinceLastSent = Date.now() - this.otpLastSent;
    const cooldownMs = cooldownSeconds * 1000;

    if (timeSinceLastSent < cooldownMs) {
      return {
        canRequest: false,
        reason: 'cooldown_active',
        remainingTime: Math.ceil((cooldownMs - timeSinceLastSent) / 1000)
      };
    }
  }

  return { canRequest: true };
};

// Increment OTP attempts
TempUserSchema.methods.incrementOTPAttempts = function() {
  this.otpAttempts = (this.otpAttempts || 0) + 1;

  const maxAttempts = parseInt(process.env.OTP_MAX_ATTEMPTS) || 3;

  // Set cooldown if max attempts reached
  if (this.otpAttempts >= maxAttempts) {
    this.otpCooldownUntil = Date.now() + (60 * 60 * 1000); // 1 hour cooldown
  }
};

// Reset OTP attempts
TempUserSchema.methods.resetOTPAttempts = function() {
  this.otpAttempts = 0;
  this.otpCooldownUntil = undefined;
};

module.exports = mongoose.model('TempUser', TempUserSchema);
