import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { uploadProfileImage } from '../../redux/slices/authSlice';
import './SellerOnboardingStep1.css';

const SellerOnboardingStep1 = ({ formData, onInputChange, onExperienceChange, onAddExperience, onNext }) => {
  const dispatch = useDispatch();
  const { isLoading: uploadLoading } = useSelector((state) => state.auth);
  const [selectedFile, setSelectedFile] = useState(null);
  const [previewUrl, setPreviewUrl] = useState(formData.profilePic || null);

  // Sync previewUrl with formData.profilePic changes
  useEffect(() => {
    if (formData.profilePic && formData.profilePic !== previewUrl) {
      setPreviewUrl(formData.profilePic);
    }
  }, [formData.profilePic, previewUrl]);

  const handleFileSelect = (e) => {
    const file = e.target.files[0];
    if (file) {
      setSelectedFile(file);
      // Create preview URL
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreviewUrl(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) return;

    try {
      console.log('Starting upload for file:', selectedFile.name);
      const result = await dispatch(uploadProfileImage(selectedFile)).unwrap();
      console.log('Upload successful:', result);

      const uploadedUrl = result.data.fileUrl;
      console.log('Uploaded URL:', uploadedUrl);

      // Update form data with the uploaded URL
      onInputChange('profilePic', uploadedUrl);

      // Update preview to show the uploaded image from server
      setPreviewUrl(uploadedUrl);

      // Clear selected file
      setSelectedFile(null);

      console.log('Profile picture updated successfully');
      alert('Image uploaded successfully!');
    } catch (error) {
      console.error('Upload failed:', error);
      alert('Failed to upload image. Please try again.');
    }
  };

  return (
    <div className="seller-onboarding-step1-container max-container">
      {/* Progress Bar */}
      <div className="progress-bar">
        <div className="step active">1</div>
        <div className="progress-line" />
        <div className="step">2</div>
      </div>

      <div className="form-grid">
        {/* Description Section */}
        <div className="description-section">
          <div className="section-title">Description</div>
          <div className="description-box">
            <textarea
              className="description-textarea"
              placeholder="Write Description.."
              rows={3}
              value={formData.description}
              onChange={e => onInputChange('description', e.target.value)}
            />
          </div>
        </div>

        {/* Profile Pic & Experience Section */}
        <div className="profile-experience-grid">
          {/* Profile Pic */}
          <div className="profile-pic-section">
            <div className="section-title">Profile Pic</div>
            <div className="avatar-upload">
              <div className="avatar-placeholder">
                {previewUrl || formData.profilePic ? (
                  <img
                    src={previewUrl || formData.profilePic}
                    alt="Profile"
                    className="avatar-image"
                  />
                ) : (
                  <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="32" cy="32" r="32" fill="var(--light-gray)" />
                    <ellipse cx="32" cy="27" rx="12" ry="12" fill="#fff" />
                    <ellipse cx="32" cy="50" rx="16" ry="10" fill="#fff" />
                  </svg>
                )}
              </div>
              <input
                type="file"
                id="profilePicInput"
                accept="image/*"
                onChange={handleFileSelect}
                style={{ display: 'none' }}
              />
              <div className="upload-buttons">
                <button
                  type="button"
                  className="btn btn-outline upload-btn"
                  onClick={() => document.getElementById('profilePicInput').click()}
                >
                  Choose Photo
                </button>
                {selectedFile && (
                  <button
                    type="button"
                    className="btn btn-primary upload-btn"
                    onClick={handleUpload}
                    disabled={uploadLoading}
                  >
                    {uploadLoading ? 'Uploading...' : 'Upload'}
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Experience */}
          <div className="experience-section">
            <div className="section-title">Experience</div>
            {formData.experiences.map((exp, idx) => (
              <div className="experience-row" key={idx}>
                <input
                  type="text"
                  className="input"
                  placeholder="Enter School Name"
                  value={exp.schoolName}
                  onChange={e => onExperienceChange(idx, 'schoolName', e.target.value)}
                />
                <input
                  type="text"
                  className="input"
                  placeholder="Enter Position"
                  value={exp.position}
                  onChange={e => onExperienceChange(idx, 'position', e.target.value)}
                />
                <div className="year-fields">
                  <input
                    type="text"
                    className="input year-input"
                    placeholder="From Year"
                    value={exp.fromYear}
                    onChange={e => onExperienceChange(idx, 'fromYear', e.target.value)}
                  />
                  <input
                    type="text"
                    className="input year-input"
                    placeholder="To Year"
                    value={exp.toYear}
                    onChange={e => onExperienceChange(idx, 'toYear', e.target.value)}
                  />
                </div>
              </div>
            ))}
            <div className="add-more-link" onClick={onAddExperience}>
              + Add More
            </div>
          </div>
        </div>
      </div>



      {/* Next Button */}
      <div className="next-btn-row">
        <button className="btn btn-primary next-btn" onClick={onNext}>Next</button>
      </div>
    </div>
  );
};

export default SellerOnboardingStep1;