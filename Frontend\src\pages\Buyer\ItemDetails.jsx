import React from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import {
  selectStrategyById,
  selectAllStrategies,
} from "../../redux/slices/buyerDashboardSlice";
import StrategyCard from "../../components/common/StrategyCard";
import "../../styles/StrategyCard.css";

const ItemDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const strategy = useSelector((state) => selectStrategyById(state, id));
  const allStrategies = useSelector(selectAllStrategies);
  const related = allStrategies.filter((s) => String(s.id) !== String(id));

  if (!strategy) return <div className="item-details">Not found</div>;

  return (
    <div
      className="item-details details-page"
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "2rem",
        padding: "2rem 0",
      }}
    >
      <div
        className="max-container"
        style={{ display: "flex", flexWrap: "wrap", gap: "2rem" }}
      >
        <div className="main-content" style={{ flex: 2, minWidth: 0 }}>
          <h2 style={{ color: "var(--secondary-color)", marginBottom: "1rem" }}>
            {strategy.title}
          </h2>
          <img
            src={strategy.image}
            alt={strategy.title}
            style={{
              width: "100%",
              maxWidth: 500,
              borderRadius: "var(--border-radius-large)",
              marginBottom: "1rem",
            }}
          />
          <div
            className="video-placeholder"
            style={{
              background: "var(--light-gray)",
              height: 250,
              borderRadius: 12,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              marginBottom: 24,
            }}
          >
            <span style={{ color: "var(--dark-gray)" }}>
              [Video Player Placeholder]
            </span>
          </div>
          <div
            className="tabs-placeholder"
            style={{ display: "flex", gap: 24, marginBottom: 24 }}
          >
            <div
              style={{
                flex: 1,
                background: "var(--white)",
                borderRadius: 8,
                padding: 16,
                border: "1px solid var(--light-gray)",
              }}
            >
              <strong>Description</strong>
              <p style={{ marginTop: 8, color: "var(--dark-gray)" }}>
                This is a placeholder for the strategy description. Add more
                details about the strategy here.
              </p>
            </div>
            <div
              style={{
                flex: 1,
                background: "var(--white)",
                borderRadius: 8,
                padding: 16,
                border: "1px solid var(--light-gray)",
              }}
            >
              <strong>The Coach</strong>
              <p style={{ marginTop: 8, color: "var(--dark-gray)" }}>
                {strategy.coach}
              </p>
            </div>
          </div>
          <button
            className={`action-button ${
              strategy.type === "bid" ? "bid-button" : "buy-button"
            }`}
            style={{ minWidth: 120, fontSize: 18 }}
          >
            {strategy.type === "bid" ? "Bid Now" : "Buy Now"}
          </button>
        </div>
        <aside
          className="sidebar"
          style={{
            flex: 1,
            minWidth: 260,
            background: "var(--white)",
            borderRadius: 12,
            padding: 24,
            border: "1px solid var(--light-gray)",
            height: "fit-content",
          }}
        >
          <div className="price-info" style={{ marginBottom: 16 }}>
            <div
              style={{
                fontWeight: 700,
                fontSize: 22,
                color: "var(--secondary-color)",
              }}
            >
              ${strategy.price.toFixed(2)}
            </div>
            <div style={{ color: "var(--dark-gray)", marginTop: 8 }}>
              Dummy info: This is a sidebar for price and other details.
            </div>
          </div>
        </aside>
      </div>
      <section
        className="related-strategies max-container"
        style={{ width: "100%" }}
      >
        <h3 style={{ color: "var(--secondary-color)", marginBottom: 16 }}>
          Related Strategies
        </h3>
        <div
          className="strategy-grid"
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(auto-fit, minmax(260px, 1fr))",
            gap: 24,
          }}
        >
          {related.slice(0, 4).map((card) => (
            <StrategyCard
              key={card.id}
              id={card.id}
              image={card.image}
              title={card.title}
              coach={card.coach}
              price={card.price}
              hasVideo={card.hasVideo}
              type={card.type || "buy"}
            />
          ))}
        </div>
      </section>
    </div>
  );
};

export default ItemDetails;
